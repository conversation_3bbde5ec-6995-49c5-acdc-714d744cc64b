# See https://docs.docker.com/engine/reference/builder/#dockerignore-file

# Dependencies
node_modules
.pnpm-store

# Next.js build output
.next
out

# Environment files
.env*
!.env.example

# Development files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.DS_Store

# Git
.git
.gitignore

# IDE
.idea
.vscode

# Testing
coverage
.nyc_output

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
README.md
LICENSE
*.md
.husky
.github 
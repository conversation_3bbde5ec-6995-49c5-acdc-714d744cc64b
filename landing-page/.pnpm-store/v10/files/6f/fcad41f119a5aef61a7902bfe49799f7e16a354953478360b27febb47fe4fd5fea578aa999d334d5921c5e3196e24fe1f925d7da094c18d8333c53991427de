/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconArrowWaveLeftUp = createReactComponent("outline", "arrow-wave-left-up", "IconArrowWaveLeftUp", [["path", { "d": "M7 10h-4v4", "key": "svg-0" }], ["path", { "d": "M21 12c-.887 -1.285 -2.48 -2.033 -4 -2c-1.52 -.033 -3.113 .715 -4 2c-.887 1.284 -2.48 2.033 -4 2c-1.52 .033 -3 -1 -4 -2l-2 -2", "key": "svg-1" }]]);

export { IconArrowWaveLeftUp as default };
//# sourceMappingURL=IconArrowWaveLeftUp.mjs.map

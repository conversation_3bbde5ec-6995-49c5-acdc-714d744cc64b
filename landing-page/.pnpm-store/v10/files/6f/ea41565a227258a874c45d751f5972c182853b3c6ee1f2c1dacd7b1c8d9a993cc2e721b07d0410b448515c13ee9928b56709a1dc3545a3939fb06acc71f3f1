/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCropPortraitFilled = createReactComponent("filled", "crop-portrait-filled", "IconCropPortraitFilled", [["path", { "d": "M16 3a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-8a3 3 0 0 1 -3 -3v-12a3 3 0 0 1 3 -3z", "key": "svg-0" }]]);

export { IconCropPortraitFilled as default };
//# sourceMappingURL=IconCropPortraitFilled.mjs.map

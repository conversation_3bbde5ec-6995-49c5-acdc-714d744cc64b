/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconLetterYSmall = createReactComponent("outline", "letter-y-small", "IconLetterYSmall", [["path", { "d": "M10 8l2 5l2 -5", "key": "svg-0" }], ["path", { "d": "M12 16v-3", "key": "svg-1" }]]);

export { IconLetterYSmall as default };
//# sourceMappingURL=IconLetterYSmall.mjs.map

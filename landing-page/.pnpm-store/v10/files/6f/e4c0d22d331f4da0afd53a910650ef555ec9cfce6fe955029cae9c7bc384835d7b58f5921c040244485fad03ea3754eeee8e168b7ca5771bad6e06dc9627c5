{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "names": ["makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "serverCapturedErrors", "basePath", "flushedErrorMetaTagsUntilIndex", "hasUnflushedPolyfills", "length", "getServerInsertedHTML", "errorMetaTags", "error", "isNotFoundError", "push", "meta", "name", "content", "digest", "process", "env", "NODE_ENV", "isRedirectError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "statusCode", "getRedirectStatusCodeFromError", "isPermanent", "RedirectStatusCode", "PermanentRedirect", "id", "httpEquiv", "serverInsertedHTML", "Array", "isArray", "stream", "renderToReadableStream", "map", "polyfill", "script", "src", "progressiveChunkSize", "streamToString"], "mappings": ";;;;+BAYgBA;;;eAAAA;;;;8DAZE;0BACc;0BAKzB;4BACgC;sCACR;oCACI;+BACL;;;;;;AAEvB,SAASA,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,oBAAoB,EACpBC,QAAQ,EAMT;IACC,IAAIC,iCAAiC;IACrC,IAAIC,wBAAwBL,UAAUM,MAAM,KAAK;IAEjD,OAAO,eAAeC;QACpB,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOJ,iCAAiCF,qBAAqBI,MAAM,CAAE;YACnE,MAAMG,QAAQP,oBAAoB,CAACE,+BAA+B;YAClEA;YAEA,IAAIM,IAAAA,yBAAe,EAACD,QAAQ;gBAC1BD,cAAcG,IAAI,eAChB,qBAACC;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeL,MAAMM,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,qBAACN;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIK,IAAAA,yBAAe,EAACV,QAAQ;gBACjC,MAAMW,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACb,QACxBN;gBAEF,MAAMoB,aAAaC,IAAAA,wCAA8B,EAACf;gBAClD,MAAMgB,cACJF,eAAeG,sCAAkB,CAACC,iBAAiB,GAAG,OAAO;gBAC/D,IAAIP,aAAa;oBACfZ,cAAcG,IAAI,eAChB,qBAACC;wBACCgB,IAAG;wBACHC,WAAU;wBACVf,SAAS,CAAC,EAAEW,cAAc,IAAI,EAAE,KAAK,EAAEL,YAAY,CAAC;uBAC/CX,MAAMM,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMe,qBAAqB7B;QAE3B,wDAAwD;QACxD,IACE,CAACI,yBACDG,cAAcF,MAAM,KAAK,KACzByB,MAAMC,OAAO,CAACF,uBACdA,mBAAmBxB,MAAM,KAAK,GAC9B;YACA,OAAO;QACT;QAEA,MAAM2B,SAAS,MAAMC,IAAAA,kCAAsB,gBACzC;;gBAEI,0DAA0D,GAC1D7B,yBACEL,UAAUmC,GAAG,CAAC,CAACC;oBACb,qBAAO,qBAACC;wBAA2B,GAAGD,QAAQ;uBAA1BA,SAASE,GAAG;gBAClC;gBAEHR;gBACAtB;;YAEH;YACE,yDAAyD;YACzD,uBAAuB;YACvB+B,sBAAsB,OAAO;QAC/B;QAGFlC,wBAAwB;QAExB,qDAAqD;QACrD,qEAAqE;QACrE,oEAAoE;QACpE,OAAOmC,IAAAA,oCAAc,EAACP;IACxB;AACF"}
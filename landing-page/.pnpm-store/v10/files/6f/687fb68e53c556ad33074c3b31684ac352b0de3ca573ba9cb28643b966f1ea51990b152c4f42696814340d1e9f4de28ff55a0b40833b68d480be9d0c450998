/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconDeviceIpadSearch = createReactComponent("outline", "device-ipad-search", "IconDeviceIpadSearch", [["path", { "d": "M11.5 21h-5.5a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v6", "key": "svg-0" }], ["path", { "d": "M9 18h2", "key": "svg-1" }], ["path", { "d": "M18 18m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }], ["path", { "d": "M20.2 20.2l1.8 1.8", "key": "svg-3" }]]);

export { IconDeviceIpadSearch as default };
//# sourceMappingURL=IconDeviceIpadSearch.mjs.map

/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconLetterT = createReactComponent("outline", "letter-t", "IconLetterT", [["path", { "d": "M6 4l12 0", "key": "svg-0" }], ["path", { "d": "M12 4l0 16", "key": "svg-1" }]]);

export { IconLetterT as default };
//# sourceMappingURL=IconLetterT.mjs.map

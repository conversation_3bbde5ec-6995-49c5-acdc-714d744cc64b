/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconMathIntegralX = createReactComponent("outline", "math-integral-x", "IconMathIntegralX", [["path", { "d": "M3 19a2 2 0 0 0 2 2c2 0 2 -4 3 -9s1 -9 3 -9a2 2 0 0 1 2 2", "key": "svg-0" }], ["path", { "d": "M14 12l6 6", "key": "svg-1" }], ["path", { "d": "M14 18l6 -6", "key": "svg-2" }]]);

export { IconMathIntegralX as default };
//# sourceMappingURL=IconMathIntegralX.mjs.map

{"version": 3, "file": "getINP.js", "sources": ["../../../../src/browser/web-vitals/getINP.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bindReporter } from './lib/bindReporter';\nimport { initMetric } from './lib/initMetric';\nimport { observe } from './lib/observe';\nimport { onHidden } from './lib/onHidden';\nimport { getInteractionCount, initInteractionCountPolyfill } from './lib/polyfills/interactionCountPolyfill';\nimport type { ReportCallback, ReportOpts } from './types';\nimport type { INPMetric } from './types/inp';\n\ninterface Interaction {\n  id: number;\n  latency: number;\n  entries: PerformanceEventTiming[];\n}\n\n/**\n * Returns the interaction count since the last bfcache restore (or for the\n * full page lifecycle if there were no bfcache restores).\n */\nconst getInteractionCountForNavigation = (): number => {\n  return getInteractionCount();\n};\n\n// To prevent unnecessary memory usage on pages with lots of interactions,\n// store at most 10 of the longest interactions to consider as INP candidates.\nconst MAX_INTERACTIONS_TO_CONSIDER = 10;\n\n// A list of longest interactions on the page (by latency) sorted so the\n// longest one is first. The list is as most MAX_INTERACTIONS_TO_CONSIDER long.\nconst longestInteractionList: Interaction[] = [];\n\n// A mapping of longest interactions by their interaction ID.\n// This is used for faster lookup.\nconst longestInteractionMap: { [interactionId: string]: Interaction } = {};\n\n/**\n * Takes a performance entry and adds it to the list of worst interactions\n * if its duration is long enough to make it among the worst. If the\n * entry is part of an existing interaction, it is merged and the latency\n * and entries list is updated as needed.\n */\nconst processEntry = (entry: PerformanceEventTiming): void => {\n  // The least-long of the 10 longest interactions.\n  const minLongestInteraction = longestInteractionList[longestInteractionList.length - 1];\n\n  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n  const existingInteraction = longestInteractionMap[entry.interactionId!];\n\n  // Only process the entry if it's possibly one of the ten longest,\n  // or if it's part of an existing interaction.\n  if (\n    existingInteraction ||\n    longestInteractionList.length < MAX_INTERACTIONS_TO_CONSIDER ||\n    entry.duration > minLongestInteraction.latency\n  ) {\n    // If the interaction already exists, update it. Otherwise create one.\n    if (existingInteraction) {\n      existingInteraction.entries.push(entry);\n      existingInteraction.latency = Math.max(existingInteraction.latency, entry.duration);\n    } else {\n      const interaction = {\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        id: entry.interactionId!,\n        latency: entry.duration,\n        entries: [entry],\n      };\n      longestInteractionMap[interaction.id] = interaction;\n      longestInteractionList.push(interaction);\n    }\n\n    // Sort the entries by latency (descending) and keep only the top ten.\n    longestInteractionList.sort((a, b) => b.latency - a.latency);\n    longestInteractionList.splice(MAX_INTERACTIONS_TO_CONSIDER).forEach(i => {\n      // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n      delete longestInteractionMap[i.id];\n    });\n  }\n};\n\n/**\n * Returns the estimated p98 longest interaction based on the stored\n * interaction candidates and the interaction count for the current page.\n */\nconst estimateP98LongestInteraction = (): Interaction => {\n  const candidateInteractionIndex = Math.min(\n    longestInteractionList.length - 1,\n    Math.floor(getInteractionCountForNavigation() / 50),\n  );\n\n  return longestInteractionList[candidateInteractionIndex];\n};\n\n/**\n * Calculates the [INP](https://web.dev/responsiveness/) value for the current\n * page and calls the `callback` function once the value is ready, along with\n * the `event` performance entries reported for that interaction. The reported\n * value is a `DOMHighResTimeStamp`.\n *\n * A custom `durationThreshold` configuration option can optionally be passed to\n * control what `event-timing` entries are considered for INP reporting. The\n * default threshold is `40`, which means INP scores of less than 40 are\n * reported as 0. Note that this will not affect your 75th percentile INP value\n * unless that value is also less than 40 (well below the recommended\n * [good](https://web.dev/inp/#what-is-a-good-inp-score) threshold).\n *\n * If the `reportAllChanges` configuration option is set to `true`, the\n * `callback` function will be called as soon as the value is initially\n * determined as well as any time the value changes throughout the page\n * lifespan.\n *\n * _**Important:** INP should be continually monitored for changes throughout\n * the entire lifespan of a page—including if the user returns to the page after\n * it's been hidden/backgrounded. However, since browsers often [will not fire\n * additional callbacks once the user has backgrounded a\n * page](https://developer.chrome.com/blog/page-lifecycle-api/#advice-hidden),\n * `callback` is always called when the page's visibility state changes to\n * hidden. As a result, the `callback` function might be called multiple times\n * during the same page load._\n */\nexport const onINP = (onReport: ReportCallback, opts?: ReportOpts): void => {\n  // Set defaults\n  // eslint-disable-next-line no-param-reassign\n  opts = opts || {};\n\n  // https://web.dev/inp/#what's-a-%22good%22-inp-value\n  // const thresholds = [200, 500];\n\n  // TODO(philipwalton): remove once the polyfill is no longer needed.\n  initInteractionCountPolyfill();\n\n  const metric = initMetric('INP');\n  // eslint-disable-next-line prefer-const\n  let report: ReturnType<typeof bindReporter>;\n\n  const handleEntries = (entries: INPMetric['entries']): void => {\n    entries.forEach(entry => {\n      if (entry.interactionId) {\n        processEntry(entry);\n      }\n\n      // Entries of type `first-input` don't currently have an `interactionId`,\n      // so to consider them in INP we have to first check that an existing\n      // entry doesn't match the `duration` and `startTime`.\n      // Note that this logic assumes that `event` entries are dispatched\n      // before `first-input` entries. This is true in Chrome but it is not\n      // true in Firefox; however, Firefox doesn't support interactionId, so\n      // it's not an issue at the moment.\n      // TODO(philipwalton): remove once crbug.com/1325826 is fixed.\n      if (entry.entryType === 'first-input') {\n        const noMatchingEntry = !longestInteractionList.some(interaction => {\n          return interaction.entries.some(prevEntry => {\n            return entry.duration === prevEntry.duration && entry.startTime === prevEntry.startTime;\n          });\n        });\n        if (noMatchingEntry) {\n          processEntry(entry);\n        }\n      }\n    });\n\n    const inp = estimateP98LongestInteraction();\n\n    if (inp && inp.latency !== metric.value) {\n      metric.value = inp.latency;\n      metric.entries = inp.entries;\n      report();\n    }\n  };\n\n  const po = observe('event', handleEntries, {\n    // Event Timing entries have their durations rounded to the nearest 8ms,\n    // so a duration of 40ms would be any event that spans 2.5 or more frames\n    // at 60Hz. This threshold is chosen to strike a balance between usefulness\n    // and performance. Running this callback for any interaction that spans\n    // just one or two frames is likely not worth the insight that could be\n    // gained.\n    durationThreshold: opts.durationThreshold || 40,\n  } as PerformanceObserverInit);\n\n  report = bindReporter(onReport, metric, opts.reportAllChanges);\n\n  if (po) {\n    // Also observe entries of type `first-input`. This is useful in cases\n    // where the first interaction is less than the `durationThreshold`.\n    po.observe({ type: 'first-input', buffered: true });\n\n    onHidden(() => {\n      handleEntries(po.takeRecords() as INPMetric['entries']);\n\n      // If the interaction count shows that there were interactions but\n      // none were captured by the PerformanceObserver, report a latency of 0.\n      if (metric.value < 0 && getInteractionCountForNavigation() > 0) {\n        metric.value = 0;\n        metric.entries = [];\n      }\n\n      report(true);\n    });\n  }\n};\n"], "names": ["getInteractionCount", "initInteractionCountPolyfill", "initMetric", "observe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onHidden"], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAgBA;AACA;AACA;AACA;AACA,MAAM,gCAAA,GAAmC,MAAc;AACvD,EAAE,OAAOA,4CAAmB,EAAE,CAAA;AAC9B,CAAC,CAAA;AACD;AACA;AACA;AACA,MAAM,4BAAA,GAA+B,EAAE,CAAA;AACvC;AACA;AACA;AACA,MAAM,sBAAsB,GAAkB,EAAE,CAAA;AAChD;AACA;AACA;AACA,MAAM,qBAAqB,GAA6C,EAAE,CAAA;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,YAAa,GAAE,CAAC,KAAK,KAAmC;AAC9D;AACA,EAAE,MAAM,qBAAsB,GAAE,sBAAsB,CAAC,sBAAsB,CAAC,MAAA,GAAS,CAAC,CAAC,CAAA;AACzF;AACA;AACA,EAAE,MAAM,sBAAsB,qBAAqB,CAAC,KAAK,CAAC,aAAa,CAAE,CAAA;AACzE;AACA;AACA;AACA,EAAE;AACF,IAAI,mBAAoB;AACxB,IAAI,sBAAsB,CAAC,MAAO,GAAE,4BAA6B;AACjE,IAAI,KAAK,CAAC,QAAS,GAAE,qBAAqB,CAAC,OAAA;AAC3C,IAAI;AACJ;AACA,IAAI,IAAI,mBAAmB,EAAE;AAC7B,MAAM,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC7C,MAAM,mBAAmB,CAAC,OAAQ,GAAE,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAA;AACzF,WAAW;AACX,MAAM,MAAM,cAAc;AAC1B;AACA,QAAQ,EAAE,EAAE,KAAK,CAAC,aAAa;AAC/B,QAAQ,OAAO,EAAE,KAAK,CAAC,QAAQ;AAC/B,QAAQ,OAAO,EAAE,CAAC,KAAK,CAAC;AACxB,OAAO,CAAA;AACP,MAAM,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAA,GAAI,WAAW,CAAA;AACzD,MAAM,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;AAC9C,KAAI;AACJ;AACA;AACA,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,OAAA,GAAU,CAAC,CAAC,OAAO,CAAC,CAAA;AAChE,IAAI,sBAAsB,CAAC,MAAM,CAAC,4BAA4B,CAAC,CAAC,OAAO,CAAC,CAAA,IAAK;AAC7E;AACA,MAAM,OAAO,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;AACxC,KAAK,CAAC,CAAA;AACN,GAAE;AACF,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA,MAAM,6BAAA,GAAgC,MAAmB;AACzD,EAAE,MAAM,yBAAA,GAA4B,IAAI,CAAC,GAAG;AAC5C,IAAI,sBAAsB,CAAC,MAAO,GAAE,CAAC;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAC,GAAI,EAAE,CAAC;AACvD,GAAG,CAAA;AACH;AACA,EAAE,OAAO,sBAAsB,CAAC,yBAAyB,CAAC,CAAA;AAC1D,CAAC,CAAA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACa,KAAM,GAAE,CAAC,QAAQ,EAAkB,IAAI,KAAwB;AAC5E;AACA;AACA,EAAE,IAAK,GAAE,IAAK,IAAG,EAAE,CAAA;AACnB;AACA;AACA;AACA;AACA;AACA,EAAEC,qDAA4B,EAAE,CAAA;AAChC;AACA,EAAE,MAAM,MAAO,GAAEC,qBAAU,CAAC,KAAK,CAAC,CAAA;AAClC;AACA,EAAE,IAAI,MAAM,CAAA;AACZ;AACA,EAAE,MAAM,aAAA,GAAgB,CAAC,OAAO,KAAiC;AACjE,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;AAC7B,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AAC/B,QAAQ,YAAY,CAAC,KAAK,CAAC,CAAA;AAC3B,OAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,KAAK,CAAC,SAAU,KAAI,aAAa,EAAE;AAC7C,QAAQ,MAAM,kBAAkB,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAA,IAAe;AAC5E,UAAU,OAAO,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;AACvD,YAAY,OAAO,KAAK,CAAC,QAAA,KAAa,SAAS,CAAC,QAAS,IAAG,KAAK,CAAC,SAAA,KAAc,SAAS,CAAC,SAAS,CAAA;AACnG,WAAW,CAAC,CAAA;AACZ,SAAS,CAAC,CAAA;AACV,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,YAAY,CAAC,KAAK,CAAC,CAAA;AAC7B,SAAQ;AACR,OAAM;AACN,KAAK,CAAC,CAAA;AACN;AACA,IAAI,MAAM,GAAA,GAAM,6BAA6B,EAAE,CAAA;AAC/C;AACA,IAAI,IAAI,GAAA,IAAO,GAAG,CAAC,OAAA,KAAY,MAAM,CAAC,KAAK,EAAE;AAC7C,MAAM,MAAM,CAAC,KAAA,GAAQ,GAAG,CAAC,OAAO,CAAA;AAChC,MAAM,MAAM,CAAC,OAAA,GAAU,GAAG,CAAC,OAAO,CAAA;AAClC,MAAM,MAAM,EAAE,CAAA;AACd,KAAI;AACJ,GAAG,CAAA;AACH;AACA,EAAE,MAAM,KAAKC,eAAO,CAAC,OAAO,EAAE,aAAa,EAAE;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,EAAE,IAAI,CAAC,iBAAA,IAAqB,EAAE;AACnD,KAA+B,CAAA;AAC/B;AACA,EAAE,MAAA,GAASC,yBAAY,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;AAChE;AACA,EAAE,IAAI,EAAE,EAAE;AACV;AACA;AACA,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,IAAA,EAAM,CAAC,CAAA;AACvD;AACA,IAAIC,iBAAQ,CAAC,MAAM;AACnB,MAAM,aAAa,CAAC,EAAE,CAAC,WAAW,IAA2B,CAAA;AAC7D;AACA;AACA;AACA,MAAM,IAAI,MAAM,CAAC,KAAM,GAAE,CAAE,IAAG,gCAAgC,EAAG,GAAE,CAAC,EAAE;AACtE,QAAQ,MAAM,CAAC,KAAM,GAAE,CAAC,CAAA;AACxB,QAAQ,MAAM,CAAC,OAAQ,GAAE,EAAE,CAAA;AAC3B,OAAM;AACN;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,CAAA;AAClB,KAAK,CAAC,CAAA;AACN,GAAE;AACF;;;;"}
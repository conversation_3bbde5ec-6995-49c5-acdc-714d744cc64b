import type { getPrivacyOptions } from './getPrivacyOptions';
interface MaskAttributeParams {
    maskAttributes: string[];
    maskAllText: boolean;
    privacyOptions: ReturnType<typeof getPrivacyOptions>;
    key: string;
    value: string;
    el: HTMLElement;
}
/**
 * Masks an attribute if necessary, otherwise return attribute value as-is.
 */
export declare function maskAttribute({ el, key, maskAttributes, maskAllText, privacyOptions, value, }: MaskAttributeParams): string;
export {};
//# sourceMappingURL=maskAttribute.d.ts.map
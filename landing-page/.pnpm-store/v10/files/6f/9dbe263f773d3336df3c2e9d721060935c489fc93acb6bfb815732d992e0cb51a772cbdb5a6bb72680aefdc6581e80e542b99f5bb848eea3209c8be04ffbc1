/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconNumber16Small = createReactComponent("outline", "number-16-small", "IconNumber16Small", [["path", { "d": "M8 8h1v8", "key": "svg-0" }], ["path", { "d": "M17 9a1 1 0 0 0 -1 -1h-2a1 1 0 0 0 -1 1v6a1 1 0 0 0 1 1h2a1 1 0 0 0 1 -1v-2a1 1 0 0 0 -1 -1h-3", "key": "svg-1" }]]);

export { IconNumber16Small as default };
//# sourceMappingURL=IconNumber16Small.mjs.map

{"version": 3, "file": "IFetchInterceptor.d.ts", "sourceRoot": "", "sources": ["../../../src/fetch/types/IFetchInterceptor.ts"], "names": [], "mappings": "AAAA,OAAO,OAAO,MAAM,eAAe,CAAC;AACpC,OAAO,aAAa,MAAM,+BAA+B,CAAC;AAC1D,OAAO,QAAQ,MAAM,gBAAgB,CAAC;AACtC,OAAO,aAAa,MAAM,oBAAoB,CAAC;AAE/C,MAAM,CAAC,OAAO,WAAW,iBAAiB;IACzC;;;;;;;OAOG;IACH,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE;QAC9B,OAAO,EAAE,OAAO,CAAC;QACjB,MAAM,EAAE,aAAa,CAAC;KACtB,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;IAE/B;;;;;;;OAOG;IACH,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE;QAC7B,OAAO,EAAE,OAAO,CAAC;QACjB,MAAM,EAAE,aAAa,CAAC;KACtB,KAAK,aAAa,GAAG,IAAI,CAAC;IAE3B;;;;;;;OAOG;IACH,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE;QAC9B,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,QAAQ,CAAC;QACnB,MAAM,EAAE,aAAa,CAAC;KACtB,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;IAE/B;;;;;;;OAOG;IACH,iBAAiB,CAAC,EAAE,CAAC,OAAO,EAAE;QAC7B,OAAO,EAAE,OAAO,CAAC;QACjB,QAAQ,EAAE,aAAa,CAAC;QACxB,MAAM,EAAE,aAAa,CAAC;KACtB,KAAK,aAAa,GAAG,IAAI,CAAC;CAC3B"}
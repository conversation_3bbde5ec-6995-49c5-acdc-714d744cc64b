/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconCloudUp = createReactComponent("outline", "cloud-up", "IconCloudUp", [["path", { "d": "M12 18.004h-5.343c-2.572 -.004 -4.657 -2.011 -4.657 -4.487c0 -2.475 2.085 -4.482 4.657 -4.482c.393 -1.762 1.794 -3.2 3.675 -3.773c1.88 -.572 3.956 -.193 5.444 1c1.488 1.19 2.162 3.007 1.77 4.769h.99c1.38 0 2.57 .811 3.128 1.986", "key": "svg-0" }], ["path", { "d": "M19 22v-6", "key": "svg-1" }], ["path", { "d": "M22 19l-3 -3l-3 3", "key": "svg-2" }]]);

export { IconCloudUp as default };
//# sourceMappingURL=IconCloudUp.mjs.map

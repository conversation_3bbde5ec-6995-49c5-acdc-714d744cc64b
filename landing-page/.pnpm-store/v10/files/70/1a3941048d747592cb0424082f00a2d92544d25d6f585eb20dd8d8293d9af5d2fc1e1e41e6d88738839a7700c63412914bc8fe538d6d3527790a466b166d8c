/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconAlignBoxTopCenter = createReactComponent("outline", "align-box-top-center", "IconAlignBoxTopCenter", [["path", { "d": "M3 3m0 2a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v14a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M9 9v-2", "key": "svg-1" }], ["path", { "d": "M12 13v-6", "key": "svg-2" }], ["path", { "d": "M15 11v-4", "key": "svg-3" }]]);

export { IconAlignBoxTopCenter as default };
//# sourceMappingURL=IconAlignBoxTopCenter.mjs.map

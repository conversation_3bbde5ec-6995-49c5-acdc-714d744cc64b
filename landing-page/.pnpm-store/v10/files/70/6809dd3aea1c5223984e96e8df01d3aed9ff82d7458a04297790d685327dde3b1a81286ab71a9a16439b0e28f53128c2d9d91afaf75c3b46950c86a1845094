{"version": 3, "sources": ["../../../../src/app-router/client/useInternalNavFun.ts"], "sourcesContent": ["import type { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';\nimport { usePathname } from 'next/navigation';\nimport { useCallback, useEffect, useTransition } from 'react';\n\nimport { removeBasePath } from '../../utils/removeBasePath';\n\nconst getClerkNavigationObject = (name: string) => {\n  window.__clerk_internal_navigations ??= {};\n  // @ts-ignore\n  window.__clerk_internal_navigations[name] ??= {};\n  return window.__clerk_internal_navigations[name];\n};\n\nexport const useInternalNavFun = (props: {\n  windowNav: typeof window.history.pushState | typeof window.history.replaceState | undefined;\n  routerNav: AppRouterInstance['push'] | AppRouterInstance['replace'];\n  name: string;\n}): NavigationFunction => {\n  const { windowNav, routerNav, name } = props;\n  const pathname = usePathname();\n  const [isPending, startTransition] = useTransition();\n\n  if (windowNav) {\n    getClerkNavigationObject(name).fun = (to, opts) => {\n      return new Promise<void>(res => {\n        // We need to use window to store the reference to the buffer,\n        // as ClerkProvider might be unmounted and remounted during navigations\n        // If we use a ref, it will be reset when ClerkProvider is unmounted\n        getClerkNavigationObject(name).promisesBuffer ??= [];\n        getClerkNavigationObject(name).promisesBuffer?.push(res);\n        startTransition(() => {\n          // If the navigation is internal, we should use the history API to navigate\n          // as this is the way to perform a shallow navigation in Next.js App Router\n          // without unmounting/remounting the page or fetching data from the server.\n          if (opts?.__internal_metadata?.navigationType === 'internal') {\n            // In 14.1.0, useSearchParams becomes reactive to shallow updates,\n            // but only if passing `null` as the history state.\n            // Older versions need to maintain the history state for push/replace to work,\n            // without affecting how the Next router works.\n            const state = ((window as any).next?.version ?? '') < '14.1.0' ? history.state : null;\n            windowNav(state, '', to);\n          } else {\n            // If the navigation is external (usually when navigating away from the component but still within the app),\n            // we should use the Next.js router to navigate as it will handle updating the URL and also\n            // fetching the new page if necessary.\n            routerNav(removeBasePath(to));\n          }\n        });\n      });\n    };\n  }\n\n  const flushPromises = () => {\n    getClerkNavigationObject(name).promisesBuffer?.forEach(resolve => resolve());\n    getClerkNavigationObject(name).promisesBuffer = [];\n  };\n\n  // Flush any pending promises on mount/unmount\n  useEffect(() => {\n    flushPromises();\n    return flushPromises;\n  }, []);\n\n  // Handle flushing the promise buffer when a navigation happens\n  useEffect(() => {\n    if (!isPending) {\n      flushPromises();\n    }\n  }, [pathname, isPending]);\n\n  return useCallback<NavigationFunction>((to, metadata) => {\n    return getClerkNavigationObject(name).fun(to, metadata);\n    // We are not expecting name to change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n};\n"], "mappings": ";AACA,SAAS,mBAAmB;AAC5B,SAAS,aAAa,WAAW,qBAAqB;AAEtD,SAAS,sBAAsB;AAE/B,MAAM,2BAA2B,CAAC,SAAiB;AANnD;AAOE,eAAO,iCAAP,mBAAO,+BAAiC,CAAC;AAEzC,qBAAO,8BAAP,iCAA8C,CAAC;AAC/C,SAAO,OAAO,6BAA6B,IAAI;AACjD;AAEO,MAAM,oBAAoB,CAAC,UAIR;AACxB,QAAM,EAAE,WAAW,WAAW,KAAK,IAAI;AACvC,QAAM,WAAW,YAAY;AAC7B,QAAM,CAAC,WAAW,eAAe,IAAI,cAAc;AAEnD,MAAI,WAAW;AACb,6BAAyB,IAAI,EAAE,MAAM,CAAC,IAAI,SAAS;AACjD,aAAO,IAAI,QAAc,SAAO;AAxBtC;AA4BQ,6CAAyB,IAAI,GAAE,mBAA/B,eAA+B,iBAAmB,CAAC;AACnD,uCAAyB,IAAI,EAAE,mBAA/B,mBAA+C,KAAK;AACpD,wBAAgB,MAAM;AA9B9B,cAAAA,KAAAC,KAAAC;AAkCU,gBAAIF,MAAA,6BAAM,wBAAN,gBAAAA,IAA2B,oBAAmB,YAAY;AAK5D,kBAAM,UAAUE,OAAAD,MAAA,OAAe,SAAf,gBAAAA,IAAqB,YAArB,OAAAC,MAAgC,MAAM,WAAW,QAAQ,QAAQ;AACjF,sBAAU,OAAO,IAAI,EAAE;AAAA,UACzB,OAAO;AAIL,sBAAU,eAAe,EAAE,CAAC;AAAA,UAC9B;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAEA,QAAM,gBAAgB,MAAM;AApD9B;AAqDI,mCAAyB,IAAI,EAAE,mBAA/B,mBAA+C,QAAQ,aAAW,QAAQ;AAC1E,6BAAyB,IAAI,EAAE,iBAAiB,CAAC;AAAA,EACnD;AAGA,YAAU,MAAM;AACd,kBAAc;AACd,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AAGL,YAAU,MAAM;AACd,QAAI,CAAC,WAAW;AACd,oBAAc;AAAA,IAChB;AAAA,EACF,GAAG,CAAC,UAAU,SAAS,CAAC;AAExB,SAAO,YAAgC,CAAC,IAAI,aAAa;AACvD,WAAO,yBAAyB,IAAI,EAAE,IAAI,IAAI,QAAQ;AAAA,EAGxD,GAAG,CAAC,CAAC;AACP;", "names": ["_a", "_b", "_c"]}
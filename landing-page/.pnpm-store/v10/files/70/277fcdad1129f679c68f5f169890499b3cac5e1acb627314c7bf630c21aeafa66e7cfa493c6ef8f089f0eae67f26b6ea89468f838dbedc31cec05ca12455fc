/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconLetterE = createReactComponent("outline", "letter-e", "IconLetterE", [["path", { "d": "M17 4h-10v16h10", "key": "svg-0" }], ["path", { "d": "M7 12l8 0", "key": "svg-1" }]]);

export { IconLetterE as default };
//# sourceMappingURL=IconLetterE.mjs.map

/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconBookmarkAi = createReactComponent("outline", "bookmark-ai", "IconBookmarkAi", [["path", { "d": "M10.02 18.32l-4.02 2.68v-14a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v4.5", "key": "svg-0" }], ["path", { "d": "M14 21v-4a2 2 0 1 1 4 0v4", "key": "svg-1" }], ["path", { "d": "M14 19h4", "key": "svg-2" }], ["path", { "d": "M21 15v6", "key": "svg-3" }]]);

export { IconBookmarkAi as default };
//# sourceMappingURL=IconBookmarkAi.mjs.map

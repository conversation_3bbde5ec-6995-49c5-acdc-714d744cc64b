/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconNumber25Small = createReactComponent("outline", "number-25-small", "IconNumber25Small", [["path", { "d": "M14 15a1 1 0 0 0 1 1h2a1 1 0 0 0 1 -1v-2a1 1 0 0 0 -1 -1h-3v-4h4", "key": "svg-0" }], ["path", { "d": "M6 8h3a1 1 0 0 1 1 1v2a1 1 0 0 1 -1 1h-2a1 1 0 0 0 -1 1v2a1 1 0 0 0 1 1h3", "key": "svg-1" }]]);

export { IconNumber25Small as default };
//# sourceMappingURL=IconNumber25Small.mjs.map

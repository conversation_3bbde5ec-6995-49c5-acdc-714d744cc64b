/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHomeCancel = createReactComponent("outline", "home-cancel", "IconHomeCancel", [["path", { "d": "M19 19m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M17 21l4 -4", "key": "svg-1" }], ["path", { "d": "M19 12h2l-9 -9l-9 9h2v7a2 2 0 0 0 2 2h5.5", "key": "svg-2" }], ["path", { "d": "M9 21v-6a2 2 0 0 1 2 -2h2c.58 0 1.103 .247 1.468 .642", "key": "svg-3" }]]);

export { IconHomeCancel as default };
//# sourceMappingURL=IconHomeCancel.mjs.map

{"version": 3, "sources": ["../../../src/trace/report/index.ts"], "names": ["reporter", "MultiReporter", "constructor", "reporters", "flushAll", "Promise", "all", "map", "report", "event", "for<PERSON>ach", "reportToJson", "reportToTelemetry"], "mappings": ";;;;+BAsBaA;;;eAAAA;;;oEArBiB;+DACL;;;;;;AAGzB,MAAMC;IAGJC,YAAYC,SAAqB,CAAE;aAF3BA,YAAwB,EAAE;QAGhC,IAAI,CAACA,SAAS,GAAGA;IACnB;IAEA,MAAMC,WAAW;QACf,MAAMC,QAAQC,GAAG,CAAC,IAAI,CAACH,SAAS,CAACI,GAAG,CAAC,CAACP,WAAaA,SAASI,QAAQ;IACtE;IAEAI,OAAOC,KAAiB,EAAE;QACxB,IAAI,CAACN,SAAS,CAACO,OAAO,CAAC,CAACV,WAAaA,SAASQ,MAAM,CAACC;IACvD;AACF;AAGO,MAAMT,WAAW,IAAIC,cAAc;IAACU,eAAY;IAAEC,oBAAiB;CAAC"}
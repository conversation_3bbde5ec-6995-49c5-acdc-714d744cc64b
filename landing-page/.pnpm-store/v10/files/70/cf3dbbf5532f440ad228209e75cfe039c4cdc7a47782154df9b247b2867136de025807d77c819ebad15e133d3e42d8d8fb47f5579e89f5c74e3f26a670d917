/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconPlaylistAdd = createReactComponent("outline", "playlist-add", "IconPlaylistAdd", [["path", { "d": "M19 8h-14", "key": "svg-0" }], ["path", { "d": "M5 12h9", "key": "svg-1" }], ["path", { "d": "M11 16h-6", "key": "svg-2" }], ["path", { "d": "M15 16h6", "key": "svg-3" }], ["path", { "d": "M18 13v6", "key": "svg-4" }]]);

export { IconPlaylistAdd as default };
//# sourceMappingURL=IconPlaylistAdd.mjs.map

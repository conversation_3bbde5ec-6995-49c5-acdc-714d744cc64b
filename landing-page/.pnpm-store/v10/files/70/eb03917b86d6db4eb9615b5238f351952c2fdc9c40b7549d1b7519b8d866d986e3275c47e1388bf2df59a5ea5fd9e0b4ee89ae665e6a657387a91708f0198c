{"version": 3, "file": "interactionCountPolyfill.js", "sources": ["../../../../../../src/browser/web-vitals/lib/polyfills/interactionCountPolyfill.ts"], "sourcesContent": ["/*\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { Metric } from '../../types';\nimport { observe } from '../observe';\n\ndeclare global {\n  interface Performance {\n    interactionCount: number;\n  }\n}\n\nlet interactionCountEstimate = 0;\nlet minKnownInteractionId = Infinity;\nlet maxKnownInteractionId = 0;\n\nconst updateEstimate = (entries: Metric['entries']): void => {\n  (entries as PerformanceEventTiming[]).forEach(e => {\n    if (e.interactionId) {\n      minKnownInteractionId = Math.min(minKnownInteractionId, e.interactionId);\n      maxKnownInteractionId = Math.max(maxKnownInteractionId, e.interactionId);\n\n      interactionCountEstimate = maxKnownInteractionId ? (maxKnownInteractionId - minKnownInteractionId) / 7 + 1 : 0;\n    }\n  });\n};\n\nlet po: PerformanceObserver | undefined;\n\n/**\n * Returns the `interactionCount` value using the native API (if available)\n * or the polyfill estimate in this module.\n */\nexport const getInteractionCount = (): number => {\n  return po ? interactionCountEstimate : performance.interactionCount || 0;\n};\n\n/**\n * Feature detects native support or initializes the polyfill if needed.\n */\nexport const initInteractionCountPolyfill = (): void => {\n  if ('interactionCount' in performance || po) return;\n\n  po = observe('event', updateEstimate, {\n    type: 'event',\n    buffered: true,\n    durationThreshold: 0,\n  } as PerformanceObserverInit);\n};\n"], "names": ["observe"], "mappings": ";;;;AAyBA,IAAI,wBAAA,GAA2B,CAAC,CAAA;AAChC,IAAI,qBAAA,GAAwB,QAAQ,CAAA;AACpC,IAAI,qBAAA,GAAwB,CAAC,CAAA;AAC7B;AACA,MAAM,cAAe,GAAE,CAAC,OAAO,KAA8B;AAC7D,EAAE,CAAC,OAAQ,GAA6B,OAAO,CAAC,KAAK;AACrD,IAAI,IAAI,CAAC,CAAC,aAAa,EAAE;AACzB,MAAM,qBAAA,GAAwB,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC,aAAa,CAAC,CAAA;AAC9E,MAAM,qBAAA,GAAwB,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC,aAAa,CAAC,CAAA;AAC9E;AACA,MAAM,wBAAyB,GAAE,qBAAsB,GAAE,CAAC,qBAAsB,GAAE,qBAAqB,IAAI,CAAA,GAAI,CAAA,GAAI,CAAC,CAAA;AACpH,KAAI;AACJ,GAAG,CAAC,CAAA;AACJ,CAAC,CAAA;AACD;AACA,IAAI,EAAE,CAAA;AACN;AACA;AACA;AACA;AACA;AACa,MAAA,mBAAA,GAAsB,MAAc;AACjD,EAAE,OAAO,KAAK,wBAAA,GAA2B,WAAW,CAAC,gBAAiB,IAAG,CAAC,CAAA;AAC1E,EAAC;AACD;AACA;AACA;AACA;AACa,MAAA,4BAAA,GAA+B,MAAY;AACxD,EAAE,IAAI,kBAAmB,IAAG,eAAe,EAAE,EAAE,OAAM;AACrD;AACA,EAAE,KAAKA,eAAO,CAAC,OAAO,EAAE,cAAc,EAAE;AACxC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,QAAQ,EAAE,IAAI;AAClB,IAAI,iBAAiB,EAAE,CAAC;AACxB,KAA+B,CAAA;AAC/B;;;;;"}
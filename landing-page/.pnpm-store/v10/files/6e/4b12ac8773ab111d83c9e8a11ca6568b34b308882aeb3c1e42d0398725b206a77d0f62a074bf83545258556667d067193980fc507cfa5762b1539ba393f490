/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconRadiusBottomRight = createReactComponent("outline", "radius-bottom-right", "IconRadiusBottomRight", [["path", { "d": "M19 5v6a8 8 0 0 1 -8 8h-6", "key": "svg-0" }]]);

export { IconRadiusBottomRight as default };
//# sourceMappingURL=IconRadiusBottomRight.mjs.map

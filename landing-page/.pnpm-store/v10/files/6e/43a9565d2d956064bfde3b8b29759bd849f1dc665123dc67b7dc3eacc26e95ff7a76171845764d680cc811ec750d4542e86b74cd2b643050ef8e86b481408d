# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.1.1](https://github.com/inspect-js/is-symbol/compare/v1.1.0...v1.1.1) - 2024-12-12

### Commits

- [actions] re-add finishers [`9b9d06f`](https://github.com/inspect-js/is-symbol/commit/9b9d06f571cf5b8481216b32474b567b02b14ae3)
- [Deps] update `call-bind`, `has-symbols`, `safe-regex-test` [`07f3647`](https://github.com/inspect-js/is-symbol/commit/07f36476b69e98353c09dc58cbcab8891e3ed2b7)
- [Refactor] use `call-bound` directly [`799402d`](https://github.com/inspect-js/is-symbol/commit/799402d3b0f291981b6406ec92c8c45cdad4e75e)
- [Dev Deps] update `@arethetypeswrong/cli`, `@ljharb/tsconfig` [`4b8b2f9`](https://github.com/inspect-js/is-symbol/commit/4b8b2f9e844ebac93e89cf8e88e08ae0e8f4cc7f)
- [types] remove unneeded DT packages [`398abaa`](https://github.com/inspect-js/is-symbol/commit/398abaaea5a6192cd0eb9fda5f0a3cfb5b1da845)

## [v1.1.0](https://github.com/inspect-js/is-symbol/compare/v1.0.4...v1.1.0) - 2024-12-02

### Commits

- [actions] reuse common workflows [`acf85f0`](https://github.com/inspect-js/is-symbol/commit/acf85f027ec6ea70a7023646c47f9324ff9a5e25)
- [meta] use `npmignore` to autogenerate an npmignore file [`77c818e`](https://github.com/inspect-js/is-symbol/commit/77c818ebf4dc1107d945854185071ca76ef94d31)
- [Tests] use `for-each` and `es-value-fixtures` [`93dfed0`](https://github.com/inspect-js/is-symbol/commit/93dfed0de6c1da2946d83017cc0f44f8f7d15ded)
- [New] add types [`ed6a057`](https://github.com/inspect-js/is-symbol/commit/ed6a057e9595fb14c7d322ed4aba3433386d07bb)
- [actions] split out node 10-20, and 20+ [`7f81ccc`](https://github.com/inspect-js/is-symbol/commit/7f81ccc8bb2c667e6975f278c9dec7310a923749)
- [Robustness] use `call-bind` and `safe-regex-test` [`dc7e142`](https://github.com/inspect-js/is-symbol/commit/dc7e142724e9dce678b1ead151c7fedd02411a03)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`70f87c2`](https://github.com/inspect-js/is-symbol/commit/70f87c2715ad4cc8e66ce0eb4a4d2c4034b8e19c)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `safe-publish-latest`, `tape` [`3f02ff4`](https://github.com/inspect-js/is-symbol/commit/3f02ff4459ec96e22be4ef8cda0c966fafb5509a)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `has-tostringtag`, `npmignore`, `object-inspect`, `tape` [`9588872`](https://github.com/inspect-js/is-symbol/commit/95888727f109c7e9d2fdfe7ed419cc8452505503)
- [actions] update rebase action to use reusable workflow [`59e2f68`](https://github.com/inspect-js/is-symbol/commit/59e2f680992f630eb1d76dd8e009d7fa074e3055)
- [actions] update codecov uploader [`e4759f8`](https://github.com/inspect-js/is-symbol/commit/e4759f8bea3b66d6d70f8b7b2656cc9b987ca874)
- [Dev Deps] update `eslint`, `auto-changelog`, `object-inspect`, `tape` [`33990c0`](https://github.com/inspect-js/is-symbol/commit/33990c0d76db7f44bcf177f7f5b602747b159a35)
- [Tests] use `has-tostringtag` for more robust Symbol.toStringTag detection [`d6154e1`](https://github.com/inspect-js/is-symbol/commit/d6154e10f79b572630fd309543160c446d7e46ef)
- [Tests] replace `aud` with `npm audit` [`3215a60`](https://github.com/inspect-js/is-symbol/commit/3215a60cf4ffce688e3911025cf2ccca95e259d0)
- [Refactor] avoid an expensive check, for primitives [`59f1a42`](https://github.com/inspect-js/is-symbol/commit/59f1a428ae625b59b618493c2454096900451d84)
- [Deps] update `has-symbols` [`06be1a9`](https://github.com/inspect-js/is-symbol/commit/06be1a9d1bf57181e35b1ffe446196243cc8becc)
- [Dev Deps] add missing peer dep [`799b0da`](https://github.com/inspect-js/is-symbol/commit/799b0da1902dfa5b02456fcf32887ead6e332358)

## [v1.0.4](https://github.com/inspect-js/is-symbol/compare/v1.0.3...v1.0.4) - 2021-05-08

### Commits

- [Tests] migrate tests to Github Actions [`997d43c`](https://github.com/inspect-js/is-symbol/commit/997d43c091d1f8d3a2b3d7dfb17a73cdc5a75dde)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`fe0ccb7`](https://github.com/inspect-js/is-symbol/commit/fe0ccb7b7b64e74e095ef782dcc1d24d6c4b0be4)
- [meta] remove unused Makefile and associated utilities [`3ab2748`](https://github.com/inspect-js/is-symbol/commit/3ab2748ab6c2de21fc24f131bb880c68ba0b7b34)
- [meta] do not publish github action workflow files [`f20fafe`](https://github.com/inspect-js/is-symbol/commit/f20fafeb21585c7b4871ea19f104fd7696734fe8)
- [Tests] run `nyc` on all tests [`5c332fc`](https://github.com/inspect-js/is-symbol/commit/5c332fc92cecbed4a2041bc0c52b991b4a593f34)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect`, `tape` [`c5a58a8`](https://github.com/inspect-js/is-symbol/commit/c5a58a8bea390a9b02e1c8c4aac30c223370297b)
- [readme] fix repo URLs; remove travis badge [`bcd9258`](https://github.com/inspect-js/is-symbol/commit/bcd9258d161fe709148fcc47962df3372c544727)
- [actions] add "Allow Edits" workflow [`33ae2d3`](https://github.com/inspect-js/is-symbol/commit/33ae2d3940e9daa6003a84c232874ee558b2fb44)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`e53def0`](https://github.com/inspect-js/is-symbol/commit/e53def0b77c38cbfae87fd8bbfd78953b845ea94)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `object-inspect` [`ae36504`](https://github.com/inspect-js/is-symbol/commit/ae365048c0c1b13457faa78658b80561f5a0bcd0)
- [readme] add actions and codecov badges [`aae7f09`](https://github.com/inspect-js/is-symbol/commit/aae7f09bd59d36df69d3b66d9b351c39fe072330)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`d993fae`](https://github.com/inspect-js/is-symbol/commit/d993fae6d89856d4ab7818874be597249cb8a8cc)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`51808a5`](https://github.com/inspect-js/is-symbol/commit/51808a55f272023201f40a59b2459ec6305bf73a)
- [Dev Deps] update `auto-changelog`, `tape` [`c90040f`](https://github.com/inspect-js/is-symbol/commit/c90040f0aeded8d0071a78d5cd593b385f8828ee)
- [Dev Deps] update `eslint`, `tape` [`9fee159`](https://github.com/inspect-js/is-symbol/commit/9fee159403d499a5ed2f5cb5db03747d09ab1766)
- [meta] use `prepublishOnly` script for npm 7+ [`b166afc`](https://github.com/inspect-js/is-symbol/commit/b166afc3ae3c6d11721a9558ddb112a28261688d)
- [meta] gitignore coverage output [`4a0fe3a`](https://github.com/inspect-js/is-symbol/commit/4a0fe3aa074b933074fcc231ce739005e1fec195)
- [actions] update workflows [`fbcbc9e`](https://github.com/inspect-js/is-symbol/commit/fbcbc9eb5bfe2cf9a77d5bd86bb1dece8e5f81d0)
- [Dev Deps] update `auto-changelog`; add `aud` [`e66ab98`](https://github.com/inspect-js/is-symbol/commit/e66ab989e48b81b48bd443d35dba0071950c5d7a)
- [Deps] update `has-symbols` [`6ce7de5`](https://github.com/inspect-js/is-symbol/commit/6ce7de53c866c068de2c28d97b3a64cf6d5f6a76)
- [actions] update rebase action to use checkout v2 [`1173c79`](https://github.com/inspect-js/is-symbol/commit/1173c79914076d73aec9aebc22dce4122e7bd3ae)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`94a6348`](https://github.com/inspect-js/is-symbol/commit/94a6348f6274eac9bf4c5a6057b4f6120fc7d1d1)
- [Tests] only audit prod deps [`0692681`](https://github.com/inspect-js/is-symbol/commit/06926811fa029fe0fded5d0af4553a7808c143d1)
- [meta] do not publish .nvmrc file [`ed47833`](https://github.com/inspect-js/is-symbol/commit/ed478333c72384f8dbeb51e5fd501238f52a4972)

## [v1.0.3](https://github.com/inspect-js/is-symbol/compare/v1.0.2...v1.0.3) - 2019-11-20

### Commits

- [Tests] use shared travis-ci configs [`034afdd`](https://github.com/inspect-js/is-symbol/commit/034afdd677c1b72b76751f3e5131acc927a32916)
- [Tests] remove `jscs` [`0c026a0`](https://github.com/inspect-js/is-symbol/commit/0c026a06815e46a33a8a5b4b1be8965d32d38e5c)
- [meta] add `auto-changelog` [`9a1776b`](https://github.com/inspect-js/is-symbol/commit/9a1776bb49f3e6ac12a5b3a447edcc651216891b)
- [Tests] up to `node` `v12.10`, `v11.15`, `v10.16`, `v8.16`, `v6.17` [`23a6db4`](https://github.com/inspect-js/is-symbol/commit/23a6db49a338d19eab19d876745513820bb6a9dc)
- [Tests] up to `node` `v11.7`, `v10.15`, `v8.15`, `v6.16` [`892d92e`](https://github.com/inspect-js/is-symbol/commit/892d92e7c40f3c0577583a98134106181c38bb7e)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `semver`, `tape` [`c2e6d6a`](https://github.com/inspect-js/is-symbol/commit/c2e6d6a71f839522bbd124b7419f5fc42ffff6d3)
- [readme] fix repo URLs [`655c288`](https://github.com/inspect-js/is-symbol/commit/655c288a815856e647dba4b6049b1743cec3533c)
- [actions] add automatic rebasing / merge commit blocking [`97b1229`](https://github.com/inspect-js/is-symbol/commit/97b12296bf8fa1ce0c6121bf3de56c413da10aae)
- [meta] add FUNDING.yml [`94c64a3`](https://github.com/inspect-js/is-symbol/commit/94c64a367a1c34f960cf6007fc65cfbbcba34ba3)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `tape`, `semver` [`71ab543`](https://github.com/inspect-js/is-symbol/commit/71ab543e09b820378362f4f66248addd410c6388)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `semver`, `tape` [`c6212f9`](https://github.com/inspect-js/is-symbol/commit/c6212f94e28622c94bb37189ffc241ee88b5b1dd)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `object-inspect` [`91bc802`](https://github.com/inspect-js/is-symbol/commit/91bc802e18e63f4e8230ee0148302ce849e2f733)
- [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops [`8cbe69c`](https://github.com/inspect-js/is-symbol/commit/8cbe69c3fafe9cfbe7d27f710c88d02d2d2c6a00)
- [Tests] use `npm audit` instead of `nsp` [`741b51d`](https://github.com/inspect-js/is-symbol/commit/741b51dac868f6b22736c204910d257bcf4d5044)
- [meta] add `funding` field [`65b58d1`](https://github.com/inspect-js/is-symbol/commit/65b58d1e9fc572712d462d615e6b2418627d8fb9)
- [Deps] update `has-symbols` [`9cb5b2a`](https://github.com/inspect-js/is-symbol/commit/9cb5b2a9a3b89e8e0246be8df4fff3f5ceac7309)

## [v1.0.2](https://github.com/inspect-js/is-symbol/compare/v1.0.1...v1.0.2) - 2018-09-20

### Commits

- Update `eslint`, `tape`, `semver`; use my personal shared `eslint` config [`e86aaea`](https://github.com/inspect-js/is-symbol/commit/e86aaea8d81356801ecfc60540523e9b809a55f4)
- [Tests] on all node minors; improve test matrix [`50bc07f`](https://github.com/inspect-js/is-symbol/commit/50bc07f2ff73e5499b02a61f0a00ea48a84ae213)
- [Dev Deps] update `tape`, `jscs`, `nsp`, `semver`, `eslint`, `@ljharb/eslint-config` [`45e17bd`](https://github.com/inspect-js/is-symbol/commit/45e17bdf145846f30122348a94c5e506b90836ba)
- [Tests] up to `node` `v10.0`, `v9.11`, `v8.11`, `v6.14`, `v4.9`; use `nvm install-latest-npm` [`44402cb`](https://github.com/inspect-js/is-symbol/commit/44402cb82d4499e947b48b31b14667d1ebe7e2b4)
- [Tests] up to `node` `v8.1`, `v7.10`, `v6.11`, `v4.8`; improve matrix; old npm breaks on newer nodes [`9047c23`](https://github.com/inspect-js/is-symbol/commit/9047c232857ecb80551a21cc0b1cc4c91d28da1f)
- Update `tape`, `covert`, `jscs`, `semver` [`d57d1ce`](https://github.com/inspect-js/is-symbol/commit/d57d1ce3fc0b740885a1ed5c0738d4a27b29ab07)
- Add `npm run eslint` [`0d75a66`](https://github.com/inspect-js/is-symbol/commit/0d75a6638ad6f7ff7d5bc958531a6328fb13e3fe)
- Update `eslint` [`042fb3a`](https://github.com/inspect-js/is-symbol/commit/042fb3aec590f0c0d205b15812b285ad95cfff6b)
- [Refactor] use `has-symbols` and `object-inspect` [`129bc68`](https://github.com/inspect-js/is-symbol/commit/129bc68dd619b789b9956ac9b63b46257ee1060c)
- [Tests] up to `node` `v10.11`, `v8.12` [`c1822e8`](https://github.com/inspect-js/is-symbol/commit/c1822e84d6cc0cee9f1c2893e91b1aa999ad41db)
- Update `tape`, `jscs`, `eslint`, `@ljharb/eslint-config` [`089d2cf`](https://github.com/inspect-js/is-symbol/commit/089d2cf7cad87b75aa534769af11524ad2e79080)
- [Tests] up to `node` `v8.4`; newer npm breaks on older node [`05ce701`](https://github.com/inspect-js/is-symbol/commit/05ce701e3c1be8b3266ffac49806832e410491c1)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`241e6a6`](https://github.com/inspect-js/is-symbol/commit/241e6a655c0e19e9dcf0ae88e7fddd4cde394c5c)
- Test on latest `node` and `io.js` versions. [`5c8d5de`](https://github.com/inspect-js/is-symbol/commit/5c8d5deb9b7c01a8cdf959082a3d619c19751b0a)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `semver`, `tape` [`06047bf`](https://github.com/inspect-js/is-symbol/commit/06047bf72b20a66c0b455e80856b2d00b1910391)
- [Dev Deps] update `jscs`, `nsp`, `semver`, `eslint`, `@ljharb/eslint-config` [`9d25dd7`](https://github.com/inspect-js/is-symbol/commit/9d25dd79347c89f98207a3bad39f667f1f8a410e)
- [Tests] up to `io.js` `v3.3`, `node` `v4.1` [`ce173bd`](https://github.com/inspect-js/is-symbol/commit/ce173bda6e146907e3061a0e70463107d955de35)
- Update `nsp`, `eslint` [`29e5214`](https://github.com/inspect-js/is-symbol/commit/29e52140fac2049b4a32e175787bb3b184a1dd72)
- Update `semver`, `eslint` [`53be884`](https://github.com/inspect-js/is-symbol/commit/53be884c2811f7a4452581003d9cdaf6f9bddd3c)
- [Dev Deps] update `eslint`, `nsp`, `semver`, `tape` [`3bd149c`](https://github.com/inspect-js/is-symbol/commit/3bd149c869c099b07104b06c0692755a01f8298c)
- [Dev Deps] update `jscs` [`69b4231`](https://github.com/inspect-js/is-symbol/commit/69b4231632b170e5ddb350db2f0c59e6cad6f548)
- Test up to `io.js` `v2.1` [`0b61ac7`](https://github.com/inspect-js/is-symbol/commit/0b61ac7ac4de390296aeefb9395549592ea87da4)
- [Dev Deps] update `tape` [`5e1b200`](https://github.com/inspect-js/is-symbol/commit/5e1b2008c910bcdabee299a1ac599143ea07c3f9)
- Only apps should have lockfiles. [`a191ff5`](https://github.com/inspect-js/is-symbol/commit/a191ff5f0320fc16db42fdaa40f0c21d4326255e)
- [Dev Deps] update `nsp`, `eslint`, `@ljharb/eslint-config` [`97c87ef`](https://github.com/inspect-js/is-symbol/commit/97c87ef52b966f211e231092a54ef6ed05c99a26)
- Test on `io.js` `v2.2` [`42560e4`](https://github.com/inspect-js/is-symbol/commit/42560e466e17cbbb9fa71c0121f4bbbcf266c887)
- [Dev Deps] Update `tape`, `eslint` [`149b2f2`](https://github.com/inspect-js/is-symbol/commit/149b2f20bde92b2da12ccfeb8988beb2dc95c37c)
- [Tests] fix test messages [`28bd1ed`](https://github.com/inspect-js/is-symbol/commit/28bd1eda310590e13ada19cbd718c85c25d8a0c5)
- Test up to `io.js` `v3.0` [`c0dcc98`](https://github.com/inspect-js/is-symbol/commit/c0dcc98313d17151ec043e5452df306618be865e)
- `node` now supports Symbols now. [`d1853ad`](https://github.com/inspect-js/is-symbol/commit/d1853adf6369ab9d4c4516bdb032c2e42f52f90a)
- [Dev Deps] update `tape` [`f7a6575`](https://github.com/inspect-js/is-symbol/commit/f7a6575fbdef13abcc412c63d22b56943ed85969)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`aae9c6a`](https://github.com/inspect-js/is-symbol/commit/aae9c6a724578659976ea74e11ec9fe35608607b)
- Test on `io.js` `v2.4` [`ab8f449`](https://github.com/inspect-js/is-symbol/commit/ab8f4492115270cc00a479915b02ac1bac75dfed)
- Test on `io.js` `v2.3` [`58ce871`](https://github.com/inspect-js/is-symbol/commit/58ce871674e857955b333aa057eeecd68b40e988)

## [v1.0.1](https://github.com/inspect-js/is-symbol/compare/v1.0.0...v1.0.1) - 2015-01-26

### Commits

- Correct package description. [`f4d15b9`](https://github.com/inspect-js/is-symbol/commit/f4d15b928b4b754b097a84f7c3ceac73c486aceb)

## v1.0.0 - 2015-01-24

### Commits

- Dotfiles. [`5d9a744`](https://github.com/inspect-js/is-symbol/commit/5d9a7441f724630070e9bd74a995191cafa1064b)
- Tests. [`8af5663`](https://github.com/inspect-js/is-symbol/commit/8af56631950dcee48b36f517837273193a6ba119)
- `make release` [`6293446`](https://github.com/inspect-js/is-symbol/commit/629344654a72e7fc8059607d6a86c64b002c3e5d)
- package.json [`7d4082c`](https://github.com/inspect-js/is-symbol/commit/7d4082ca9502118e70d24f526704d45a1a7f2067)
- Initial commit [`cbb179f`](https://github.com/inspect-js/is-symbol/commit/cbb179f677bd3dcb56ac5e3f0a7a9af503fd8952)
- Read me. [`099a775`](https://github.com/inspect-js/is-symbol/commit/099a775e7e751706283ae1cab7a8635c094773a9)
- Implementation. [`cb51248`](https://github.com/inspect-js/is-symbol/commit/cb51248eedaf55e0b8ad7dacdab179db2d76e96e)

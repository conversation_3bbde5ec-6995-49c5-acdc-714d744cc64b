/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconSeparatorHorizontal = createReactComponent("outline", "separator-horizontal", "IconSeparatorHorizontal", [["path", { "d": "M4 12l16 0", "key": "svg-0" }], ["path", { "d": "M8 8l4 -4l4 4", "key": "svg-1" }], ["path", { "d": "M16 16l-4 4l-4 -4", "key": "svg-2" }]]);

export { IconSeparatorHorizontal as default };
//# sourceMappingURL=IconSeparatorHorizontal.mjs.map

/**
 * @license @tabler/icons-react v3.34.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconHttpPutOff = createReactComponent("outline", "http-put-off", "IconHttpPutOff", [["path", { "d": "M3 12h2a2 2 0 1 0 0 -4h-2v8", "key": "svg-0" }], ["path", { "d": "M17 8h4", "key": "svg-1" }], ["path", { "d": "M19 8v8", "key": "svg-2" }], ["path", { "d": "M10 10v4a2 2 0 1 0 4 0m0 -4v-2", "key": "svg-3" }], ["path", { "d": "M3 3l18 18", "key": "svg-4" }]]);

export { IconHttpPutOff as default };
//# sourceMappingURL=IconHttpPutOff.mjs.map

{"name": "@sentry/browser", "version": "7.120.3", "description": "Official Sentry SDK for browsers", "repository": "git://github.com/getsentry/sentry-javascript.git", "homepage": "https://github.com/getsentry/sentry-javascript/tree/master/packages/browser", "author": "Sentry", "license": "MIT", "engines": {"node": ">=8"}, "files": ["cjs", "esm", "types", "types-ts3.8"], "main": "cjs/index.js", "module": "esm/index.js", "types": "types/index.d.ts", "typesVersions": {"<4.9": {"types/index.d.ts": ["types-ts3.8/index.d.ts"]}}, "publishConfig": {"access": "public", "tag": "v7"}, "dependencies": {"@sentry-internal/feedback": "7.120.3", "@sentry-internal/replay-canvas": "7.120.3", "@sentry-internal/tracing": "7.120.3", "@sentry/core": "7.120.3", "@sentry/integrations": "7.120.3", "@sentry/replay": "7.120.3", "@sentry/types": "7.120.3", "@sentry/utils": "7.120.3"}, "devDependencies": {"@sentry-internal/integration-shims": "7.120.3", "@types/md5": "2.1.33", "btoa": "^1.2.1", "chai": "^4.1.2", "chokidar": "^3.0.2", "fake-indexeddb": "^4.0.1", "karma": "^6.3.16", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^2.1.2", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.5", "karma-rollup-preprocessor": "^7.0.0", "karma-sinon": "^1.0.5", "karma-typescript": "^4.0.0", "karma-typescript-es6-transform": "^4.0.0", "karma-webkit-launcher": "^1.0.2", "mocha": "^6.1.4", "node-fetch": "^2.6.0", "playwright": "^1.31.1", "sinon": "^7.3.2", "webpack": "^4.47.0"}, "sideEffects": false}
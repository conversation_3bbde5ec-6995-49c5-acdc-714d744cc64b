.PHONY: help build up down logs install dev build-prod clean restart shell db-migrate db-reset
help: 
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install:
	docker compose -f docker/docker-compose.yml run --rm landing-page pnpm install

build:
	docker compose -f docker/docker-compose.yml build

up:
	docker compose -f docker/docker-compose.yml up -d

down:
	docker compose -f docker/docker-compose.yml down

logs:
	docker compose -f docker/docker-compose.yml logs -f

dev: up

build-prod:
	TARGET=production docker compose -f docker/docker-compose.yml build

clean:
	docker compose -f docker/docker-compose.yml down -v
	docker system prune -f

restart: down up

shell:
	docker compose -f docker/docker-compose.yml exec landing-page sh

db-migrate:
	docker compose -f docker/docker-compose.yml exec landing-page npx prisma db push

db-reset:
	docker compose -f docker/docker-compose.yml exec landing-page npx prisma db push --force-reset

test:
	docker compose -f docker/docker-compose.yml exec landing-page pnpm test

lint:
	docker compose -f docker/docker-compose.yml exec landing-page pnpm lint

format:
	docker compose -f docker/docker-compose.yml exec landing-page pnpm format

waitlist-export: ## Export waitlist data to JSON file
	docker compose -f docker/docker-compose.yml exec landing-page pnpm run export:waitlist

test: ## Run all tests
	docker compose -f docker/docker-compose.yml exec landing-page pnpm test

test-watch: ## Run tests in watch mode
	docker compose -f docker/docker-compose.yml exec landing-page pnpm run test:watch

test-coverage: ## Run tests with coverage
	docker compose -f docker/docker-compose.yml exec landing-page pnpm run test:coverage

test-ui: ## Run tests with UI
	docker compose -f docker/docker-compose.yml exec landing-page pnpm run test:ui

test-waitlist: ## Run comprehensive waitlist flow integration tests
	docker compose -f docker/docker-compose.yml exec landing-page pnpm run test:waitlist 